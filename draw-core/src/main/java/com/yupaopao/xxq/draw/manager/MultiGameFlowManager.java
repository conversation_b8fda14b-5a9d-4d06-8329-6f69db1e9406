package com.yupaopao.xxq.draw.manager;
import java.util.Date;

import com.google.common.collect.Lists;
import com.yupaopao.framework.spring.boot.redis.RedisService;
import com.yupaopao.live.dto.GameRoomDTO;
import com.yupaopao.live.dto.GameStateDTO;
import com.yupaopao.live.dto.LiveDTO;
import com.yupaopao.live.enums.LiveGameEnum;
import com.yupaopao.platform.common.dto.Code;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.platform.common.exception.YppRunTimeException;
import com.yupaopao.platform.common.po.Client;
import com.yupaopao.platform.common.utils.VersionUtil;
import com.yupaopao.platform.gaea.client.pool.IdPool;
import com.yupaopao.platform.util.trace.TraceUtil;
import com.yupaopao.room.base.dto.request.room.RTCEndRequest;
import com.yupaopao.room.base.dto.request.room.RTCStartRequest;
import com.yupaopao.room.base.dto.response.RTCRoomConfigV2DTO;
import com.yupaopao.room.base.dto.response.RTCStartDTO;
import com.yupaopao.xxq.draw.config.GameMatchOpenOnceApollo;
import com.yupaopao.xxq.draw.config.SudApolloConfig;
import com.yupaopao.xxq.draw.consts.*;
import com.yupaopao.xxq.draw.context.entity.RtcGameInfo;
import com.yupaopao.xxq.draw.domain.*;
import com.yupaopao.xxq.draw.exception.ErrorCode;
import com.yupaopao.xxq.draw.integration.MultiGameChatroomMsgSender;
import com.yupaopao.xxq.draw.integration.rpc.GameRoomQueryRPC;
import com.yupaopao.xxq.draw.integration.rpc.GameStateRPC;
import com.yupaopao.xxq.draw.integration.rpc.LiveStreamServiceRpc;
import com.yupaopao.xxq.draw.integration.rpc.SonaRPC;
import com.yupaopao.xxq.draw.manager.convert.MultiGameConvert;
import com.yupaopao.xxq.draw.mapper.GameOpenSwitchOnceMapper;
import com.yupaopao.xxq.draw.redis.RedisCommonService;
import com.yupaopao.xxq.draw.redis.RedisKeyEnum;
import com.yupaopao.xxq.draw.service.impl.MultiGameAnchorMatchTimeService;
import com.yupaopao.xxq.draw.service.impl.MultiGameRoundRecordService;
import com.yupaopao.xxq.draw.service.impl.interactive.InteractiveGameApplyService;
import com.yupaopao.xxq.draw.service.impl.interactive.MultiGameSeatService;
import com.yupaopao.xxq.draw.service.impl.interactive.MultiGameService;
import com.yupaopao.xxq.draw.util.DateUtil;
import com.yupaopao.xxq.draw.util.LockTemplate;
import com.yupaopao.xxq.game.enums.NewMultiGameState;
import com.yupaopao.xxq.interactive.constant.GameApplyStatusEnum;
import com.yupaopao.xxq.interactive.constant.GameTypeEnum;
import com.yupaopao.xxq.interactive.constant.GameUserTypeEnum;
import com.yupaopao.xxq.interactive.constant.OpTypeEnum;
import com.yupaopao.xxq.interactive.dto.MultiGameRTCConnectDTO;
import com.yupaopao.xxq.interactive.dto.MultiGameStartDTO;
import com.yupaopao.xxq.interactive.dto.SudGameConfigDTO;
import com.yupaopao.xxq.interactive.dto.SudUserExistInfo;
import com.yupaopao.xxq.interactive.request.MultiGameApplyCancelRequest;
import com.yupaopao.xxq.interactive.request.MultiGameApplyDisagreeRequest;
import com.yupaopao.xxq.interactive.request.OpenGameUser;
import com.yupaopao.xxq.message.consts.MessageTypeEnum;
import com.yupaopao.xxq.user.response.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.yupaopao.xxq.draw.manager.MultiGameQueryManager.SUD_MULTI_GOBANG;
import static com.yupaopao.xxq.draw.manager.LinkMultiGameManager.SUG_PREFIX;
import static com.yupaopao.xxq.draw.redis.RedisCommonService.KEY_JOINER;

/**
 * 多人小游戏-上麦、游戏流程manager
 **/
@Component
@Slf4j
public class MultiGameFlowManager {

    @Resource
    private SudApolloConfig sudApolloConfig;

    @Resource
    private RedisCommonService redisCommonService;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private MultiGameCommonManager multiGameCommonManager;

    @Resource
    protected GameStateRPC gameStateRPC;

    @Resource
    private InteractiveGameApplyService interactiveGameApplyService;

    @Resource
    private MultiGameSeatService multiGameSeatService;

    @Resource
    private MultiGameService multiGameService;

    @Resource
    private SonaRPC sonaRPC;

    @Resource
    private MultiGameChatroomMsgSender multiGameChatroomMsgSender;

    @Resource
    private LiveManager liveManager;

    @Resource
    private KafkaSender kafkaSender;

    @Resource
    private MultiGameManager multiGameManager;

    @Resource
    private GameRoomQueryRPC gameRoomQueryRPC;

    @Resource
    private LockTemplate lockTemplate;

    @Resource(name = "rtcIdPool")
    private IdPool rtcIdPool;

    @Resource
    private MultiGameAnchorMatchTimeService anchorMatchTimeService;

    @Resource
    private LiveMsgManager liveMsgManager;

    @Resource
    private MultiGameRoundRecordService multiGameRoundRecordService;

    @Resource
    private GameOpenSwitchOnceMapper gameOpenSwitchOnceMapper;

    public static final String CLOSED = "CLOSED";
    @Resource
    private GameMatchOpenOnceApollo gameMatchOpenOnceApollo;
    @Resource
    private UserManager userManager;
    @Resource
    private LiveStreamServiceRpc liveStreamServiceRpc;

    /**
     * 主播同意用户申请
     */
    public Boolean agreeApply(Long anchorUid, Long liveRoomId, Long applyId) {
        // 游戏同意需要是房间力度的锁，防止并发
        RedisService.RedisLocker redisLocker = redisCommonService.buildLock(RedisKeyEnum.MULT_GAME_RTC_KEY, liveRoomId);
        if (!redisLocker.tryLock()) {
            throw new YppRunTimeException(ErrorCode.REPEAT_ERROR);
        }
        try {
            // 申请信息错误或者状态不对-抛异常
            InteractiveGameApplyDO applyDO = interactiveGameApplyService.findById(applyId);
            if (applyDO == null || !GameApplyStatusEnum.isWait(applyDO.getStatus())) {
                throw new YppRunTimeException(ErrorCode.APPLY_REPEAT_NOT_EXITS);
            }
            // 校验直播间状态
            LiveDTO liveDTO = multiGameCommonManager.checkLiveInfo(liveRoomId);
            // 校验游戏冲突
            GameStateDTO conflict = gameStateRPC.getConflict(liveRoomId, LiveGameEnum.GAMING_TOGETHER.getScene());
            if (Objects.nonNull(conflict)) {
                throw new YppRunTimeException(ErrorCode.getOverrideMsg(ErrorCode.CONFLICT_STATE, conflict.getToast()));
            }

            // 查询游戏配置信息
            MultiGameConfigDO multiGameConfig = multiGameService.findMultiGameConfig(liveRoomId);
            if (multiGameConfig == null) {
                throw new YppRunTimeException(ErrorCode.GAME_INFO_ERROR);
            }

            // 校验用户
            Code errorCode = multiGameCommonManager.checkPlayer(applyDO.getUid(),
                    anchorUid,
                    liveRoomId,
                    GameUserTypeEnum.ANCHOR.getType(),
                    applyDO.getType(),
                    applyDO.getScene(),
                    multiGameConfig.getGameScene());
            if (Objects.nonNull(errorCode)) {
                interactiveGameApplyService.updateStatus(applyId, GameApplyStatusEnum.RELEASE.getStatus());
                throw new YppRunTimeException(errorCode);
            }
            // 没有空麦位了
            MultiGameSeatDO multiGameSeatDO = multiGameSeatService.findEmptySeat(liveRoomId);
            if (multiGameSeatDO == null) {
                throw new YppRunTimeException(ErrorCode.EMPTY_SEAT_ERROR);
            }

            if (GameTypeEnum.USER_APPLY.getType().equals(applyDO.getType())) {
                agreeApplyInner(liveRoomId, applyId, multiGameConfig, applyDO, liveDTO, multiGameSeatDO);
            } else {
                // 处理申请状态
                interactiveGameApplyService.updateStatus(applyDO.getId(), GameApplyStatusEnum.SUCCESS.getStatus());
                // 发送同意 kafka 消息
                multiGameChatroomMsgSender.sendApplyStatusMsg(applyDO.getAnchorUid(),
                        applyDO.getLiveRoomId(),
                        applyDO.getUid(),
                        GameApplyStatusEnum.SUCCESS.getStatus());
            }
            return true;
        } finally {
            redisLocker.release();
        }
    }

    private void agreeApplyInner(Long liveRoomId, Long applyId, MultiGameConfigDO multiGameConfig, InteractiveGameApplyDO applyDO, LiveDTO liveDTO, MultiGameSeatDO multiGameSeatDO) {
        // 房间内用户上麦直接连麦
        RTCRoomConfigV2DTO rtcRoomConfigV2DTO = null;
        String rtcBizId = multiGameConfig.getRtcBizId();
        String gameRoomSonaId = queryGameRoomSonaId(liveRoomId);

        // 第一次需要创建rtc
        if (StringUtils.isEmpty(rtcBizId)) {
            // 初始化rtcBizId:用applyId作为连麦的rtcBizId
            rtcBizId = applyId.toString();
            RTCStartDTO rtcStartDTO = rtcStart(applyDO.getUid(), applyDO.getAppId(), liveDTO, rtcBizId, multiGameConfig.getGameScene(), gameRoomSonaId);
            // 开始连麦，暂时关闭原来的直播流的审核
            liveStreamServiceRpc.endAudit(liveRoomId);
            // 修改多人小游戏rtc配置信息
            multiGameService.updateStartRTC(liveDTO.getLiveRoomId(), rtcStartDTO.getRtcRoomId(), rtcBizId);
        } else {
            // 加入rtc房间
            rtcRoomConfigV2DTO = sonaRPC.enterRTCRoom(applyDO.getAppId(), applyDO.getUid(), multiGameConfig.getRtcRoomId(), liveDTO.getSonaRoomId(), gameRoomSonaId);
        }
        // 坐上麦位
        multiGameSeatService.seatDown(multiGameSeatDO, liveRoomId, applyDO.getUid(), applyDO.getAppId(), applyDO.getScene(), RoomTypeEnum.LIVE_ROOM.getRoomType());
        // 修改游戏配置信息(如果主播本身没有选择游戏，需要自动选择出用户的意向游戏)
        boolean autoChooseGame = multiGameService.autoUpdateGameScene(multiGameConfig, applyDO.getScene());
        // 处理申请状态
        interactiveGameApplyService.updateStatus(applyDO.getId(), GameApplyStatusEnum.SUCCESS.getStatus());
        // 维护麦位使用数量
        int usedSeatCount = multiGameService.updateUsedSeatCount(liveRoomId);
        // 维护一起玩
        multiGameCommonManager.addGameState(liveRoomId, LiveGameEnum.GAMING_TOGETHER.name());
        // 发送rtc配置消息
        multiGameChatroomMsgSender.sendRtcConfig(liveRoomId, applyDO.getUid(), rtcBizId, rtcRoomConfigV2DTO, gameRoomSonaId);
        if (NewMultiGameState.isStarting(multiGameConfig.getNewGameState())
                && multiGameConfig.getGameScene().startsWith(SUG_PREFIX)) {
            UserInfo userInfo = userManager.getRoomUser(applyDO.getUid(), liveRoomId);
            SudGameConfigDTO sudGameConfigDTO = sudApolloConfig.querySudGameConfigMap(liveRoomId).get(multiGameConfig.getGameScene());
            multiGameCommonManager.sendSugUserEnterMsg(sudGameConfigDTO, applyDO.getUid(), liveRoomId, userInfo);
        }
        // 发送麦位置变更消息
        multiGameChatroomMsgSender.sendSeatChangeMsg(liveRoomId, applyDO.getUid(), multiGameSeatDO.getId(), multiGameSeatDO.getSeatNo(), MultiGameSeatState.SIT.getCode(), autoChooseGame, multiGameConfig.getGameScene(), MultiGameSeatChangeType.SIT_APPLY);
        // 发送sud房间匹配消息
        multiGameChatroomMsgSender.sendMatchKafkaMsg(
                OpTypeEnum.UPDATE,
                multiGameConfig.getAnchorUid(),
                usedSeatCount,
                multiGameConfig.getMatchSwitch() == 1,
                multiGameConfig.getNewGameState(),
                multiGameConfig.getGameScene(),
                null,
                liveRoomId);
    }


    /**
     * 用户同意主播邀请
     */
    public Boolean agreeInvite(Long uid, Long liveRoomId, Long applyId) {
        // 游戏同意需要是房间力度的锁，防止并发
        RedisService.RedisLocker redisLocker = redisCommonService.buildLock(RedisKeyEnum.MULT_GAME_RTC_KEY, liveRoomId);
        if (!redisLocker.tryLock()) {
            throw new YppRunTimeException(ErrorCode.REPEAT_ERROR);
        }
        try {
            // 申请信息错误或者状态不对-抛异常
            InteractiveGameApplyDO applyDO = interactiveGameApplyService.findById(applyId);
            if (applyDO == null || !GameApplyStatusEnum.isWait(applyDO.getStatus())) {
                throw new YppRunTimeException(ErrorCode.APPLY_REPEAT_NOT_EXITS);
            }
            // 校验直播间状态
            LiveDTO liveDTO = multiGameCommonManager.checkLiveInfo(liveRoomId);
            // 查询游戏配置信息
            MultiGameConfigDO multiGameConfig = multiGameService.findMultiGameConfig(liveRoomId);
            if (multiGameConfig == null) {
                interactiveGameApplyService.updateStatus(applyId, GameApplyStatusEnum.RELEASE.getStatus());
                throw new YppRunTimeException(ErrorCode.GAME_INFO_ERROR);
            }
            // 校验用户
            Code errorCode = multiGameCommonManager.checkPlayer(applyDO.getUid(),
                    liveDTO.getUid(),
                    liveRoomId,
                    GameUserTypeEnum.USER.getType(),
                    GameTypeEnum.ANCHOR_INVITE.getType(),
                    applyDO.getScene(),
                    multiGameConfig.getGameScene());
            if (Objects.nonNull(errorCode)) {
                interactiveGameApplyService.updateStatus(applyId, GameApplyStatusEnum.RELEASE.getStatus());
                throw new YppRunTimeException(errorCode);
            }
            // 查询用户麦位情况
            MultiGameSeatDO multiGameSeatDO = multiGameSeatService.findSeatByUid(liveRoomId, uid);
            if (multiGameSeatDO == null || MultiGameSeatState.OCCUPY.getCode() != multiGameSeatDO.getState()) {
                interactiveGameApplyService.updateStatus(applyId, GameApplyStatusEnum.RELEASE.getStatus());
                throw new YppRunTimeException(ErrorCode.SEAT_INFO_ERROR);
            }

            RTCRoomConfigV2DTO rtcRoomConfigV2DTO = null;
            String rtcBizId = multiGameConfig.getRtcBizId();
            String gameRoomSonaId = queryGameRoomSonaId(liveRoomId);

            // 第一次需要创建rtc
            if (StringUtils.isEmpty(rtcBizId)) {
                // 初始化rtcBizId:用applyId作为连麦的rtcBizId
                rtcBizId = applyId.toString();
                RTCStartDTO rtcStartDTO = rtcStart(applyDO.getUid(), applyDO.getAppId(), liveDTO, rtcBizId, multiGameConfig.getGameScene(), gameRoomSonaId);
                // 开始连麦，暂时关闭原来的直播流的审核
                liveStreamServiceRpc.endAudit(liveRoomId);
                // 修改多人小游戏rtc配置信息
                multiGameService.updateStartRTC(liveDTO.getLiveRoomId(), rtcStartDTO.getRtcRoomId(), rtcBizId);
            } else {
                // 加入rtc房间
                rtcRoomConfigV2DTO = sonaRPC.enterRTCRoom(applyDO.getAppId(), applyDO.getUid(), multiGameConfig.getRtcRoomId(), liveDTO.getSonaRoomId(), gameRoomSonaId);
            }
            // 坐上麦位
            multiGameSeatService.seatDown(multiGameSeatDO, liveRoomId, applyDO.getUid(), applyDO.getAppId(), applyDO.getScene(), RoomTypeEnum.LIVE_ROOM.getRoomType());
            if (NewMultiGameState.isStarting(multiGameConfig.getNewGameState())
                    && multiGameConfig.getGameScene().startsWith(SUG_PREFIX)) {
                UserInfo userInfo = userManager.getRoomUser(applyDO.getUid(), liveRoomId);
                SudGameConfigDTO sudGameConfigDTO = sudApolloConfig.querySudGameConfigMap(liveRoomId).get(multiGameConfig.getGameScene());
                multiGameCommonManager.sendSugUserEnterMsg(sudGameConfigDTO, applyDO.getUid(), liveRoomId, userInfo);
            }
            // 处理申请状态
            interactiveGameApplyService.updateStatus(applyDO.getId(), GameApplyStatusEnum.SUCCESS.getStatus());
            // 维护麦位使用数量
            multiGameService.updateUsedSeatCount(liveRoomId);
            // 维护一起玩
            multiGameCommonManager.addGameState(liveRoomId, LiveGameEnum.GAMING_TOGETHER.name());
            // 发送rtc配置消息
            multiGameChatroomMsgSender.sendRtcConfig(liveRoomId, applyDO.getUid(), rtcBizId, rtcRoomConfigV2DTO, gameRoomSonaId);
            // 发送麦位置变更消息
            multiGameChatroomMsgSender.sendSeatChangeMsg(liveRoomId, applyDO.getUid(), multiGameSeatDO.getId(), multiGameSeatDO.getSeatNo(), MultiGameSeatState.SIT.getCode(), false, multiGameConfig.getGameScene(), MultiGameSeatChangeType.SIT_INVITE);
            return true;
        } finally {
            redisLocker.release();
        }
    }

    /**
     * 从sud房间上麦
     */
    public Boolean seatDownFromGameRoom(Long uid, Long liveRoomId, Integer appId, String gameScene) {
        // 上麦需要是房间力度的锁，防止并发
        String key = RedisKeyEnum.MULT_GAME_RTC_KEY.getRedisKeyPattern().concat(KEY_JOINER.join(new Object[]{liveRoomId}));
        lockTemplate.execute(key, () -> {
            // 没有空麦位了
            MultiGameSeatDO multiGameSeatDO = multiGameSeatService.findEmptySeat(liveRoomId);
            if (multiGameSeatDO == null) {
                log.info("seatDownFromGameRoom没有空麦位了, liveRoomId:{}", liveRoomId);
                throw new YppRunTimeException(ErrorCode.EMPTY_SEAT_ERROR);
            }

            // 校验直播间状态
            LiveDTO liveDTO = multiGameCommonManager.checkLiveInfo(liveRoomId);
            // 校验游戏冲突
            GameStateDTO conflict = gameStateRPC.getConflict(liveRoomId, LiveGameEnum.GAMING_TOGETHER.getScene());
            if (Objects.nonNull(conflict)) {
                log.info("seatDownFromGameRoom游戏冲突, liveRoomId:{}, conflict:{}", liveRoomId, conflict);
                throw new YppRunTimeException(ErrorCode.getOverrideMsg(ErrorCode.CONFLICT_STATE, conflict.getToast()));
            }

            // 校验用户
            Code errorCode = multiGameCommonManager.checkSudRoomPlayer(uid, liveDTO.getUid(), liveRoomId, GameUserTypeEnum.USER.getType(), getMarket());
            if (Objects.nonNull(errorCode)) {
                log.info("seatDownFromGameRoom校验用户失败, liveRoomId:{}, errorCode:{}", liveRoomId, errorCode);
                throw new YppRunTimeException(errorCode);
            }

            // 查询游戏配置信息
            MultiGameConfigDO multiGameConfig = multiGameService.findMultiGameConfig(liveRoomId);
            if (multiGameConfig == null) {
                log.info("seatDownFromGameRoom查询游戏配置信息失败, liveRoomId:{}", liveRoomId);
                throw new YppRunTimeException(ErrorCode.GAME_INFO_ERROR);
            }

            RTCRoomConfigV2DTO rtcRoomConfigV2DTO = null;
            String rtcBizId = multiGameConfig.getRtcBizId();
            String gameRoomSonaId = queryGameRoomSonaId(liveRoomId);

            // 第一次需要创建rtc
            if (StringUtils.isEmpty(rtcBizId)) {
                // 初始化rtcBizId:用gaea生成
                rtcBizId = rtcIdPool.poll();
                RTCStartDTO rtcStartDTO = rtcStart(uid, appId, liveDTO, rtcBizId, multiGameConfig.getGameScene(), gameRoomSonaId);
                // 开始连麦，暂时关闭原来的直播流的审核
                liveStreamServiceRpc.endAudit(liveRoomId);
                // 修改多人小游戏rtc配置信息
                multiGameService.updateStartRTC(liveDTO.getLiveRoomId(), rtcStartDTO.getRtcRoomId(), rtcBizId);
            } else {
                // 加入rtc房间
                rtcRoomConfigV2DTO = sonaRPC.enterRTCRoom(appId, uid, multiGameConfig.getRtcRoomId(), liveDTO.getSonaRoomId(), gameRoomSonaId);
            }
            // 坐上麦位
            multiGameSeatService.seatDown(multiGameSeatDO, liveRoomId, uid, appId, gameScene, RoomTypeEnum.GAME_ROOM.getRoomType());
            if (NewMultiGameState.isStarting(multiGameConfig.getNewGameState())
                    && multiGameConfig.getGameScene().startsWith(SUG_PREFIX)) {
                UserInfo userInfo = userManager.getRoomUser(uid, liveRoomId);
                SudGameConfigDTO sudGameConfigDTO = sudApolloConfig.querySudGameConfigMap(liveRoomId).get(multiGameConfig.getGameScene());
                multiGameCommonManager.sendSugUserEnterMsg(sudGameConfigDTO, uid, liveRoomId, userInfo);
            }
            // 设置 sud用户所在房间的 exist 信息
            multiGameService.setSudUserExist(uid, new SudUserExistInfo(liveRoomId, gameScene, multiGameSeatDO.getId(), multiGameSeatDO.getSeatNo(), multiGameConfig.getAnchorUid()));
            // 修改游戏配置信息(如果主播本身没有选择游戏，需要自动选择出用户的意向游戏)
            boolean autoChooseGame = multiGameService.autoUpdateGameScene(multiGameConfig, gameScene);
            // 维护麦位使用数量
            int usedSeatCount = multiGameService.updateUsedSeatCount(liveRoomId);
            // 维护一起玩
            multiGameCommonManager.addGameState(liveRoomId, LiveGameEnum.GAMING_TOGETHER.name());
            // 发送rtc配置消息
            multiGameChatroomMsgSender.sendRtcConfig(liveRoomId, uid, rtcBizId, rtcRoomConfigV2DTO, gameRoomSonaId);
            // 发送麦位置变更消息
            multiGameChatroomMsgSender.sendSeatChangeMsg(liveRoomId, uid, multiGameSeatDO.getId(), multiGameSeatDO.getSeatNo(), MultiGameSeatState.SIT.getCode(), autoChooseGame, multiGameConfig.getGameScene(), MultiGameSeatChangeType.SIT_APPLY);
            // 发送sud房间匹配消息
            multiGameChatroomMsgSender.sendMatchKafkaMsg(
                    OpTypeEnum.UPDATE,
                    multiGameConfig.getAnchorUid(),
                    usedSeatCount,
                    multiGameConfig.getMatchSwitch() == 1,
                    multiGameConfig.getNewGameState(),
                    multiGameConfig.getGameScene(),
                    null,
                    liveRoomId);
            log.info("seatDownFromGameRoom上麦成功, liveRoomId:{}, uid:{}", liveRoomId, uid);
            return true;
        });
        return true;
    }


    public MultiGameRTCConnectDTO connectRTC(Long liveRoomId, Long uid, Integer appId) {
        MultiGameConfigDO multiGameConfig = multiGameService.findMultiGameConfig(liveRoomId);
        if (multiGameConfig == null || StringUtils.isEmpty(multiGameConfig.getRtcRoomId())) {
            return null;
        }

        RTCRoomConfigV2DTO rtcRoomConfigV2DTO = sonaRPC.getRTCConfigByRTCRoomId(uid, appId, multiGameConfig.getRtcRoomId(), SUDGameConstant.SUD_MULTI_LINK_SCENE);
        GameRoomDTO gameRoomDTO = gameRoomQueryRPC.findGameRoomByLiveRoomId(liveRoomId);
        SudGameConfigDTO sudGameConfigDTO = null;
        if (StringUtils.hasText(multiGameConfig.getGameScene())) {
            Map<String, SudGameConfigDTO> sudGameConfigDTOMap = sudApolloConfig.querySudGameConfigMap(liveRoomId);
            sudGameConfigDTO = sudGameConfigDTOMap.get(multiGameConfig.getGameScene());
        }

        MultiGameRTCConnectDTO result = new MultiGameRTCConnectDTO();
        result.setRtcInfo(MultiGameConvert.convertRtcInfoDTO(rtcRoomConfigV2DTO));
        result.setSudGameConfig(sudGameConfigDTO);
        result.setGameRoomSonaId(gameRoomDTO == null ? "" : gameRoomDTO.getGameSonaId());
        return result;
    }


    /**
     * 主播选择游戏
     */
    public void chooseGame(Long liveRoomId, Long anchorUid, String gameScene) {
        // 校验游戏
        multiGameCommonManager.checkScene(gameScene, liveRoomId);

        RedisService.RedisLocker redisLocker = redisCommonService.buildLock(RedisKeyEnum.MULTI_GAME_CHOOSE_KEY, anchorUid);
        if (!redisLocker.tryLock()) {
            throw new YppRunTimeException(ErrorCode.REPEAT_ERROR);
        }
        try {
            MultiGameConfigDO multiGameConfig = multiGameService.findMultiGameConfig(liveRoomId);
            if (multiGameConfig == null || !anchorUid.equals(multiGameConfig.getAnchorUid())) {
                throw new YppRunTimeException(ErrorCode.GAME_INFO_ERROR);
            }

            // 选择游戏时把老玩法清理
            if (StringUtils.hasText(multiGameConfig.getGameScene())) {
                multiGameCommonManager.removeGameState(liveRoomId, multiGameConfig.getGameScene());
            }

            // 变更游戏
            multiGameService.chooseGameScene(multiGameConfig, gameScene);

            // 发送选择游戏消息
            multiGameChatroomMsgSender.sendChooseGameMsg(liveRoomId, gameScene);

            if (multiGameConfig.getMatchSwitch() == 1) {
                // 发送sud房间匹配消息
                multiGameChatroomMsgSender.sendMatchKafkaMsg(
                        OpTypeEnum.UPDATE,
                        multiGameConfig.getAnchorUid(),
                        multiGameConfig.getUsedSeatCount(),
                        true,
                        multiGameConfig.getNewGameState(),
                        multiGameConfig.getGameScene(),
                        null,
                        liveRoomId);
            }
        } finally {
            redisLocker.release();
        }
    }

    /**
     * 主播开始游戏
     */
    public MultiGameStartDTO startGame(Long liveRoomId, Long anchorUid, String gameScene) {
        // 校验游戏
        SudGameConfigDTO sudGameConfigDTO = multiGameCommonManager.checkScene(gameScene, liveRoomId);

        RedisService.RedisLocker redisLocker = redisCommonService.buildLock(RedisKeyEnum.MULTI_GAME_START_KEY, anchorUid);
        if (!redisLocker.tryLock()) {
            throw new YppRunTimeException(ErrorCode.REPEAT_ERROR);
        }
        try {
            MultiGameConfigDO multiGameConfig = multiGameService.findMultiGameConfig(liveRoomId);
            if (multiGameConfig == null || !anchorUid.equals(multiGameConfig.getAnchorUid())) {
                throw new YppRunTimeException(ErrorCode.GAME_INFO_ERROR);
            }

            List<MultiGameSeatDO> occupiedSeats = multiGameSeatService.findGameSeatList(liveRoomId)
                    .stream()
                    .filter(seat -> seat.getState() == 2 || seat.getState() == 1)
                    .collect(Collectors.toList());
            multiGameCommonManager.checkSugPlayers(occupiedSeats, gameScene, anchorUid);

            String originGameScene = multiGameConfig.getGameScene();

            // 校验游戏冲突
            GameStateDTO conflict = gameStateRPC.getConflict(liveRoomId, LiveGameEnum.GAMING_TOGETHER.getScene());
            if (Objects.nonNull(conflict)) {
                throw new YppRunTimeException(ErrorCode.getOverrideMsg(ErrorCode.CONFLICT_STATE, conflict.getToast()));
            }

            // 开始游戏
            multiGameService.startGameScene(multiGameConfig, gameScene);

            // 移除老玩法
            if (StringUtils.hasText(originGameScene) && !originGameScene.equals(gameScene)) {
                multiGameCommonManager.removeGameState(liveRoomId, originGameScene);
            }

            // 维护新玩法
            multiGameCommonManager.addGameState(liveRoomId, gameScene);

            multiGameCommonManager.addGameState(liveRoomId, LiveGameEnum.GAMING_TOGETHER.name());


            // 如果开启曝光，开启曝光时长统计
            if (Objects.equals(1, multiGameConfig.getMatchSwitch())) {
                openMatchTimeCalculateStart(liveRoomId);
            }

            // 如果是 SUG 小游戏，需要给游戏中心发送消息
            if (gameScene.startsWith(SUG_PREFIX)) {
                List<OpenGameUser> players = filterPlayerList(liveRoomId, anchorUid, occupiedSeats);
                multiGameCommonManager.sendSugSwitchMsg(sudGameConfigDTO, anchorUid, liveRoomId, players);
            }

            // 玩法开始事件
            kafkaSender.sendGameFlow(LiveGameEnum.GAMING_TOGETHER.name(), "START", liveRoomId.toString(), anchorUid.toString(), anchorUid.toString(), multiGameConfig.getRtcBizId());

            // 发送游戏开始消息
            multiGameChatroomMsgSender.sendStartGameMsg(liveRoomId, gameScene);

            // 给用户中心发送中关注主播 "游戏中" 状态变更消息
            multiGameChatroomMsgSender.sendKafkaMsgForRelationService(anchorUid, liveRoomId, getSimpleStateDesc(NewMultiGameState.READY));

            // 发送sud房间匹配消息
            multiGameChatroomMsgSender.sendMatchKafkaMsg(
                    OpTypeEnum.UPDATE,
                    multiGameConfig.getAnchorUid(),
                    multiGameConfig.getUsedSeatCount(),
                    multiGameConfig.getMatchSwitch() == 1,
                    NewMultiGameState.READY.getCode(),
                    multiGameConfig.getGameScene(),
                    null,
                    liveRoomId);

            // 构建返回体
            return multiGameCommonManager.buildMultiGameStartDTO(gameScene, liveRoomId);

        } finally {
            redisLocker.release();
        }
    }

    /**
     * 直播间开启匹配时间统计,开始时间点
     */
    public void openMatchTimeCalculateStart(long liveRoomId) {
        long current = System.currentTimeMillis();
        redisTemplate.opsForValue().set(RedisKeyEnum.buildMatchTimeCalKey(liveRoomId),
                String.valueOf(current),
                RedisKeyEnum.OPEN_MATCH_TIME_CALCULATE.getExpireTime(), TimeUnit.SECONDS);
        redisTemplate.opsForSet().add(RedisKeyEnum.OPEN_MATCH_ROOM_ID_LIST.getRedisKeyPattern(),
                String.valueOf(liveRoomId));
    }

    /**
     * 直播间开启匹配时间统计,结束时间点
     */
    public void openMatchTimeCalculateEnd(MultiGameConfigDO multiGameConfigDO) {
        Long liveRoomId = multiGameConfigDO.getLiveRoomId();
        String key = RedisKeyEnum.buildMatchTimeCalKey(liveRoomId);
        Object o = redisTemplate.opsForValue().get(key);
        if (o == null) {
            log.info("openMatchTimeCalculateEnd, redis中没有开始时间点, liveRoomId:{}", liveRoomId);
            return;
        }
        long start = Long.parseLong((String) o);
        long end = System.currentTimeMillis();
        if (end <= start) {
            log.info("openMatchTimeCalculateEnd, 当前时间小于开始时间点, liveRoomId:{}", liveRoomId);
            return;
        }
        // 累计时间段，内部处理了跨天累计计算的逻辑
        addSegmentOpenMatchTime(start, end, multiGameConfigDO);
        redisTemplate.delete(key);
        redisTemplate.opsForSet().remove(RedisKeyEnum.OPEN_MATCH_ROOM_ID_LIST.getRedisKeyPattern(),
                String.valueOf(liveRoomId));
    }

    /**
     * 直播间开启匹配时间统计,定期累加，保存目前的进度
     */
    public void openMatchTimeCalculateSegmentUpdate(MultiGameConfigDO multiGameConfigDO) {
        Long liveRoomId = multiGameConfigDO.getLiveRoomId();
        String key = RedisKeyEnum.buildMatchTimeCalKey(liveRoomId);
        Object o = redisTemplate.opsForValue().get(key);
        if (o == null) {
            log.info("openMatchTimeCalculateSegmentUpdate, redis中没有开始时间点, liveRoomId:{}", liveRoomId);
            return;
        }
        long start = Long.valueOf((String) o);
        long end = System.currentTimeMillis();
        if (end <= start) {
            log.info("openMatchTimeCalculateSegmentUpdate, 当前时间小于开始时间点, liveRoomId:{}", liveRoomId);
            return;
        }
        // 累计时间段，内部处理了跨天累计计算的逻辑
        addSegmentOpenMatchTime(start, end, multiGameConfigDO);

        if (Objects.equals(1, multiGameConfigDO.getMatchSwitch())
                && !StringUtils.isEmpty(multiGameConfigDO.getGameScene())
                && NewMultiGameState.isStarting(multiGameConfigDO.getNewGameState())) {
            // 还在游戏中
            redisTemplate.opsForValue().setIfPresent(key,
                    String.valueOf(end));
            redisTemplate.expire(key, RedisKeyEnum.OPEN_MATCH_TIME_CALCULATE.getExpireTime(), TimeUnit.SECONDS);
        } else {
            redisTemplate.delete(key);
            redisTemplate.opsForSet().remove(RedisKeyEnum.OPEN_MATCH_ROOM_ID_LIST.getRedisKeyPattern(),
                    String.valueOf(liveRoomId));
        }
    }

    // 判断逻辑
    public void addSegmentOpenMatchTime(Long startTime, long end, MultiGameConfigDO multiGameConfigDO) {
        if (startTime == null || end <= startTime) {
            log.info("addSegmentOpenMatchTime, 开始时间点为空或者结束时间小于开始时间, startTime:{}, end:{}", startTime, end);
            return;
        }
        long startOfNextDay = DateUtil.getNextDayStart(new Date(startTime)).getTime();
        if (end > startOfNextDay) {
            long duration1 = startOfNextDay - startTime;
            long duration2 = end - startOfNextDay;
            int dt1 = DateUtil.convertToDt(startTime);
            int dt2 = DateUtil.convertToDt(end);

            MultiGameAnchorMatchTime timeDO1 = new MultiGameAnchorMatchTime();
            timeDO1.setLiveRoomId(multiGameConfigDO.getLiveRoomId());
            timeDO1.setAnchorUid(multiGameConfigDO.getAnchorUid());
            timeDO1.setBizDt(dt1);
            timeDO1.setMatchtime(duration1);
            anchorMatchTimeService.insertOrAccumulate(timeDO1);

            MultiGameAnchorMatchTime timeDO2 = new MultiGameAnchorMatchTime();
            timeDO2.setLiveRoomId(multiGameConfigDO.getLiveRoomId());
            timeDO2.setAnchorUid(multiGameConfigDO.getAnchorUid());
            timeDO2.setBizDt(dt2);
            timeDO2.setMatchtime(duration2);
            anchorMatchTimeService.insertOrAccumulate(timeDO2);
        } else {
            long duration = end - startTime;
            int dt = DateUtil.convertToDt(startTime);
            MultiGameAnchorMatchTime timeDO1 = new MultiGameAnchorMatchTime();
            timeDO1.setLiveRoomId(multiGameConfigDO.getLiveRoomId());
            timeDO1.setAnchorUid(multiGameConfigDO.getAnchorUid());
            timeDO1.setBizDt(dt);
            timeDO1.setMatchtime(duration);
            anchorMatchTimeService.insertOrAccumulate(timeDO1);
        }
    }

    /**
     * 给用用户中心发kafka 消息时候用，NONE 和 CHOSEN 聚合为 CLOSED
     * READY / PLAYING 不变
     */
    public static String getSimpleStateDesc(@NotNull NewMultiGameState state) {
        return state == NewMultiGameState.NONE || state == NewMultiGameState.CHOSEN ? CLOSED : state.name();
    }


    /**
     * 主播切换游戏
     */
    public MultiGameStartDTO changeGame(Long liveRoomId, Long anchorUid, String targetScene) {
        // 校验游戏
        SudGameConfigDTO sudGameConfigDTO = multiGameCommonManager.checkScene(targetScene, liveRoomId);

        RedisService.RedisLocker redisLocker = redisCommonService.buildLock(RedisKeyEnum.MULTI_GAME_CHANGE_KEY, anchorUid);
        if (!redisLocker.tryLock()) {
            throw new YppRunTimeException(ErrorCode.REPEAT_ERROR);
        }
        try {
            MultiGameConfigDO multiGameConfig = multiGameService.findMultiGameConfig(liveRoomId);
            if (multiGameConfig == null || !anchorUid.equals(multiGameConfig.getAnchorUid())) {
                throw new YppRunTimeException(ErrorCode.GAME_INFO_ERROR);
            }

            List<MultiGameSeatDO> occupiedSeats = multiGameSeatService.findGameSeatList(liveRoomId)
                    .stream()
                    .filter(seat -> seat.getState() == 2 || seat.getState() == 1)
                    .collect(Collectors.toList());
            multiGameCommonManager.checkSugPlayers(occupiedSeats, targetScene, anchorUid);

            String oldScene = multiGameConfig.getGameScene();

            if (targetScene.equalsIgnoreCase(oldScene)) {
                throw new YppRunTimeException(ErrorCode.GAME_REPEAT_ERROR);
            }

            // 开始游戏
            multiGameService.startGameScene(multiGameConfig, targetScene);

            // 移除老玩法
            if (StringUtils.hasText(oldScene)) {
                multiGameCommonManager.removeGameState(liveRoomId, oldScene);
            }
            // 维护新玩法
            multiGameCommonManager.addGameState(liveRoomId, targetScene);

            multiGameCommonManager.addGameState(liveRoomId, LiveGameEnum.GAMING_TOGETHER.name());

            // 如果是 SUG 小游戏，需要给游戏中心发送消息
            if (oldScene.startsWith(SUG_PREFIX)) {
                SudGameConfigDTO originGameScene = sudApolloConfig.querySudGameConfig(oldScene, null);
                multiGameCommonManager.sendSugEndMsg(originGameScene, anchorUid, liveRoomId);
            }
            if (targetScene.startsWith(SUG_PREFIX)) {
                List<OpenGameUser> players = filterPlayerList(liveRoomId, anchorUid, occupiedSeats);
                multiGameCommonManager.sendSugSwitchMsg(sudGameConfigDTO,anchorUid, liveRoomId, players);
            }

            // 玩法开始事件
            kafkaSender.sendGameFlow(LiveGameEnum.GAMING_TOGETHER.name(), "START", liveRoomId.toString(), anchorUid.toString(), anchorUid.toString(), multiGameConfig.getRtcBizId());

            // 发送游戏开始消息
            multiGameChatroomMsgSender.sendChangeGameMsg(liveRoomId, targetScene, Integer.parseInt(MessageTypeEnum.MULTI_GAME_CHANGE.getType()));

            // 发送sud房间匹配消息
            multiGameChatroomMsgSender.sendMatchKafkaMsg(
                    OpTypeEnum.UPDATE,
                    multiGameConfig.getAnchorUid(),
                    multiGameConfig.getUsedSeatCount(),
                    multiGameConfig.getMatchSwitch() == 1,
                    multiGameConfig.getNewGameState(),
                    targetScene,
                    oldScene,
                    liveRoomId);

            // 构建返回体
            return multiGameCommonManager.buildMultiGameStartDTO(targetScene, liveRoomId);
        } finally {
            redisLocker.release();
        }
    }


    /**
     * 用户主动退出连麦
     */
    public Response<Boolean> exit(Long liveRoomId, Long uid, Integer duration) {
        RedisService.RedisLocker redisLocker = redisCommonService.buildLock(RedisKeyEnum.MULTI_GAME_EXIT_KEY, uid);
        if (!redisLocker.tryLock()) {
            return Response.fail(ErrorCode.REPEAT_ERROR);
        }
        try {
            MultiGameConfigDO multiGameConfig = multiGameService.findMultiGameConfig(liveRoomId);
            if (multiGameConfig == null) {
                return Response.fail(ErrorCode.GAME_INFO_ERROR);
            }

            if (uid.equals(multiGameConfig.getAnchorUid())) {
                return Response.fail(ErrorCode.PARAM_ERROR);
            }

            // 是否正在座位上
            MultiGameSeatDO multiGameSeatDO = multiGameSeatService.findSeatByUid(liveRoomId, uid);
            if (multiGameSeatDO == null || MultiGameSeatState.SIT.getCode() != multiGameSeatDO.getState()) {
                return Response.fail(ErrorCode.SEAT_INFO_ERROR);
            }

            // 释放麦位
            multiGameSeatService.releaseSeat(multiGameSeatDO.getId(), liveRoomId);
            // 维护麦位使用数量
            int usedSeatCount = multiGameService.updateUsedSeatCount(liveRoomId);
            // 删除 sud用户所在房间的 exist 信息
            multiGameService.delSudUserExist(uid);

            // 离开rtc房间
            if (StringUtils.hasText(multiGameConfig.getRtcRoomId())) {
                sonaRPC.leaveRTCRoom(uid, multiGameConfig.getRtcRoomId());
            }

            if (NewMultiGameState.isStarting(multiGameConfig.getNewGameState())
                    && multiGameConfig.getGameScene().startsWith(SUG_PREFIX)) {
                SudGameConfigDTO sudGameConfigDTO = sudApolloConfig.querySudGameConfigMap(liveRoomId).get(multiGameConfig.getGameScene());
                multiGameCommonManager.sendSugUserLeaveMsg(sudGameConfigDTO, uid, liveRoomId);
            }

            // 闪断事件:sud游戏房&&在房时间不够则发送闪断事件
            if (RoomTypeEnum.isGameRoom(multiGameSeatDO.getSource()) && sudApolloConfig.needReportDisconnect(duration)) {
                kafkaSender.sendMultiGameRoomDisconnectMsg(liveRoomId, multiGameSeatDO.getAnchorUid(), uid);
            }

            // 离开麦位消息
            multiGameChatroomMsgSender.sendSeatChangeMsg(liveRoomId, uid, multiGameSeatDO.getId(), multiGameSeatDO.getSeatNo(), MultiGameSeatState.INIT.getCode(), false, multiGameConfig.getGameScene(), MultiGameSeatChangeType.SIT_LEAVE);

            // 发送sud房间匹配消息
            multiGameChatroomMsgSender.sendMatchKafkaMsg(
                    OpTypeEnum.UPDATE,
                    multiGameConfig.getAnchorUid(),
                    usedSeatCount,
                    multiGameConfig.getMatchSwitch() == 1,
                    multiGameConfig.getNewGameState(),
                    multiGameConfig.getGameScene(),
                    null,
                    liveRoomId);

            return Response.success(true);
        } finally {
            redisLocker.release();
        }
    }


    /**
     * 主播踢出用户下麦
     */
    public Response<Boolean> kick(Long liveRoomId, Long anchorUid, Long targetUid, MultiGameSeatChangeType multiGameSeatChangeType) {
        RedisService.RedisLocker redisLocker = redisCommonService.buildLock(RedisKeyEnum.MULTI_GAME_KICK_KEY, anchorUid);
        if (!redisLocker.tryLock()) {
            return Response.fail(ErrorCode.REPEAT_ERROR);
        }
        try {
            MultiGameConfigDO multiGameConfig = multiGameService.findMultiGameConfig(liveRoomId);
            if (multiGameConfig == null || !anchorUid.equals(multiGameConfig.getAnchorUid())) {
                return Response.fail(ErrorCode.GAME_INFO_ERROR);
            }

            if (anchorUid.equals(targetUid)) {
                return Response.fail(ErrorCode.PARAM_ERROR);
            }

            // 是否正在座位上
            MultiGameSeatDO multiGameSeatDO = multiGameSeatService.findSeatByUid(liveRoomId, targetUid);
            if (multiGameSeatDO == null) {
                return Response.fail(ErrorCode.SEAT_INFO_ERROR);
            }
            // 释放麦位
            multiGameSeatService.releaseSeat(multiGameSeatDO.getId(), liveRoomId);

            // 维护麦位使用数量
            int usedSeatCount = multiGameService.updateUsedSeatCount(liveRoomId);
            // 删除 sud用户所在房间的 exist 信息
            multiGameService.delSudUserExist(targetUid);

            if (NewMultiGameState.isStarting(multiGameConfig.getNewGameState())
                    && multiGameConfig.getGameScene().startsWith(SUG_PREFIX)) {
                SudGameConfigDTO sudGameConfigDTO = sudApolloConfig.querySudGameConfigMap(liveRoomId).get(multiGameConfig.getGameScene());
                multiGameCommonManager.sendSugUserLeaveMsg(sudGameConfigDTO, targetUid, liveRoomId);
            }

            // 发送sud房间匹配消息
            multiGameChatroomMsgSender.sendMatchKafkaMsg(
                    OpTypeEnum.UPDATE,
                    multiGameConfig.getAnchorUid(),
                    usedSeatCount,
                    multiGameConfig.getMatchSwitch() == 1,
                    multiGameConfig.getNewGameState(),
                    multiGameConfig.getGameScene(),
                    null,
                    liveRoomId);

            // 维护麦位使用数量
            multiGameService.updateUsedSeatCount(liveRoomId);

            // 离开rtc房间
            if (StringUtils.hasText(multiGameConfig.getRtcRoomId())) {
                sonaRPC.leaveRTCRoom(targetUid, multiGameConfig.getRtcRoomId());
            }

            // 麦位变更消息
            multiGameChatroomMsgSender.sendSeatChangeMsg(liveRoomId, targetUid, multiGameSeatDO.getId(), multiGameSeatDO.getSeatNo(), MultiGameSeatState.INIT.getCode(), false, multiGameConfig.getGameScene(), multiGameSeatChangeType);

            return Response.success(true);
        } finally {
            redisLocker.release();
        }
    }


    /**
     * 主播结束连麦
     */
    public Response<Boolean> end(Long liveRoomId, Long anchorUid, MultiGameEndType endType) {
        RedisService.RedisLocker redisLocker = redisCommonService.buildLock(RedisKeyEnum.MULTI_GAME_END_KEY, anchorUid);
        if (!redisLocker.tryLock()) {
            return Response.fail(ErrorCode.REPEAT_ERROR);
        }
        try {
            LiveDTO liveDTO = liveManager.getLiveRoomInfo(liveRoomId);
            if (liveDTO == null) {
                return Response.fail(ErrorCode.DATA_NOT_FOUND);
            }
            MultiGameConfigDO multiGameConfig = multiGameService.findMultiGameConfig(liveRoomId);
            if (multiGameConfig == null || !anchorUid.equals(multiGameConfig.getAnchorUid())) {
                return Response.fail(ErrorCode.GAME_INFO_ERROR);
            }

            String gameScene = multiGameConfig.getGameScene();
            SudGameConfigDTO sudGameConfigDTO = sudApolloConfig.querySudGameConfigMap(liveRoomId).get(gameScene);

            List<MultiGameSeatDO> seatList = multiGameSeatService.findGameSeatList(liveRoomId);
            if (CollectionUtils.isEmpty(seatList)) {
                return Response.fail(ErrorCode.SEAT_INFO_ERROR);
            }

            if (StringUtils.hasText(multiGameConfig.getRtcRoomId()) && StringUtils.hasText(multiGameConfig.getRtcBizId())) {
                rtcEnd(multiGameConfig.getRtcBizId(), liveDTO.getSonaRoomId(), seatList);
                // 结束连麦，恢复原来的直播流的审核
                liveStreamServiceRpc.startAudit(liveRoomId);
            }

            // 结束玩法
            if (StringUtils.hasText(gameScene)) {
                multiGameCommonManager.removeGameState(liveRoomId, gameScene);
                multiGameCommonManager.removeGameState(liveRoomId, LiveGameEnum.GAMING_TOGETHER.name());
            }

            // 如果关闭曝光，关闭曝光时长统计
            openMatchTimeCalculateEnd(multiGameConfig);

            if (gameScene.startsWith(SUG_PREFIX)) {
                // 如果是 SUG 发送结束游戏消息
                multiGameCommonManager.sendSugEndMsg(sudGameConfigDTO, liveDTO.getUid(), liveDTO.getLiveRoomId());
            }

            List<Long> uidList = seatList.stream().map(MultiGameSeatDO::getUid).filter(Objects::nonNull).collect(Collectors.toList());

            // 清理所有麦位
            multiGameSeatService.releaseAllSeat(liveRoomId);

            // 清理sud房用户 exist信息
            multiGameService.delAllSudUserExist(uidList);

            // 维护麦位使用数量
            int usedSeatCount = multiGameService.updateUsedSeatCount(liveRoomId);

            // 清理RTC配置
            multiGameService.clearConfig(liveRoomId);

            // 删除一起玩
            multiGameCommonManager.removeGameState(liveRoomId, LiveGameEnum.GAMING_TOGETHER.name());

            // 玩法结束事件
            kafkaSender.sendGameFlow(LiveGameEnum.GAMING_TOGETHER.name(), "END", liveRoomId.toString(), anchorUid.toString(), anchorUid.toString(), multiGameConfig.getRtcBizId());

            // 给用户中心发送中关注主播 "游戏中" 状态变更消息
            multiGameChatroomMsgSender.sendKafkaMsgForRelationService(anchorUid, liveRoomId, getSimpleStateDesc(NewMultiGameState.NONE));

            // 如果是正常退出并且当前匹配开关是开的，发 update 消息，如果是异常清理，发 remove 消息
            OpTypeEnum opTypeEnum = endType == MultiGameEndType.NORMAL_END && multiGameConfig.getMatchSwitch() == 1
                    ? OpTypeEnum.UPDATE
                    : OpTypeEnum.REMOVE;

            // 发送sud房间匹配消息
            multiGameChatroomMsgSender.sendMatchKafkaMsg(
                    opTypeEnum,
                    multiGameConfig.getAnchorUid(),
                    usedSeatCount,
                    multiGameConfig.getMatchSwitch() == 1,
                    NewMultiGameState.NONE.getCode(),
                    gameScene,
                    null,
                    liveRoomId);

            // 连麦结束消息
            multiGameChatroomMsgSender.sendEndMsg(liveRoomId, endType);

            return Response.success(true);
        } finally {
            redisLocker.release();
        }
    }


    public Response<Boolean> auditEnd(Long liveRoomId) {
        RedisService.RedisLocker redisLocker = redisCommonService.buildLock(RedisKeyEnum.MULTI_GAME_AUDIT_END_KEY, liveRoomId);
        if (!redisLocker.tryLock()) {
            return Response.fail(ErrorCode.REPEAT_ERROR);
        }
        try {
            LiveDTO liveDTO = liveManager.getLiveRoomInfo(liveRoomId);
            if (liveDTO == null) {
                return Response.fail(ErrorCode.DATA_NOT_FOUND);
            }

            MultiGameConfigDO multiGameConfig = multiGameService.findMultiGameConfig(liveRoomId);
            if (multiGameConfig == null) {
                return Response.fail(ErrorCode.GAME_INFO_ERROR);
            }

            String gameScene = multiGameConfig.getGameScene();
            List<MultiGameSeatDO> seatList = multiGameSeatService.findGameSeatList(liveRoomId);
            if (CollectionUtils.isEmpty(seatList)) {
                return Response.fail(ErrorCode.SEAT_INFO_ERROR);
            }

            if (StringUtils.hasText(multiGameConfig.getRtcRoomId()) && StringUtils.hasText(multiGameConfig.getRtcBizId())) {
                rtcEnd(multiGameConfig.getRtcBizId(), liveDTO.getSonaRoomId(), seatList);
                // 结束连麦，恢复原来的直播流的审核
                liveStreamServiceRpc.startAudit(liveRoomId);
            }

            // 结束玩法
            if (StringUtils.hasText(gameScene)) {
                multiGameCommonManager.removeGameState(liveRoomId, gameScene);
                multiGameCommonManager.removeGameState(liveRoomId, LiveGameEnum.GAMING_TOGETHER.name());
            }

            List<Long> uidList = seatList.stream().map(MultiGameSeatDO::getUid).filter(Objects::nonNull).collect(Collectors.toList());
            // 清理所有麦位
            multiGameSeatService.releaseAllSeat(liveRoomId);

            // 清理sud房用户 exist信息
            multiGameService.delAllSudUserExist(uidList);

            // 维护麦位使用数量
            int usedSeatCount = multiGameService.updateUsedSeatCount(liveRoomId);

            // 清理RTC配置
            multiGameService.clearConfig(liveRoomId);

            // 删除一起玩
            multiGameCommonManager.removeGameState(liveRoomId, LiveGameEnum.GAMING_TOGETHER.name());

            // 玩法结束事件
            kafkaSender.sendGameFlow(LiveGameEnum.GAMING_TOGETHER.name(), "END", liveRoomId.toString(), liveDTO.getUid().toString(), liveDTO.getUid().toString(), multiGameConfig.getRtcBizId());

            // 给用户中心发送中关注主播 "游戏中" 状态变更消息
            multiGameChatroomMsgSender.sendKafkaMsgForRelationService(liveDTO.getUid(), liveRoomId, getSimpleStateDesc(NewMultiGameState.NONE));

            // 发送sud房间匹配消息
            multiGameChatroomMsgSender.sendMatchKafkaMsg(
                    OpTypeEnum.REMOVE,
                    multiGameConfig.getAnchorUid(),
                    usedSeatCount,
                    multiGameConfig.getMatchSwitch() == 1,
                    NewMultiGameState.NONE.getCode(),
                    gameScene,
                    null,
                    liveRoomId);

            // 连麦结束消息
            multiGameChatroomMsgSender.sendEndMsg(liveRoomId, MultiGameEndType.AUDIT_END);

            return Response.success(true);
        } finally {
            redisLocker.release();
        }
    }


    public void releaseAnchor(Long liveRoomId, Long anchorUid) {

        // 结束多人小游戏
        end(liveRoomId, anchorUid, MultiGameEndType.CLEAR_END);

        // 申请清空
        interactiveGameApplyService.release(liveRoomId);

        // 维护麦位使用数量
        multiGameService.updateUsedSeatCount(liveRoomId);
    }


    public void releaseUserLeave(Long liveRoomId, Long uid) {
        // 前置结束多人小游戏
        exit(liveRoomId, uid, 0);

        // 处理多人模式申请记录
        InteractiveGameApplyDO applyDO = interactiveGameApplyService.findWaitApplyInfo(liveRoomId, uid);
        if (applyDO != null && isMultiSud(applyDO.getScene())) {
            if (GameTypeEnum.ANCHOR_INVITE.getType().equals(applyDO.getType())) {
                MultiGameApplyDisagreeRequest request = new MultiGameApplyDisagreeRequest();
                request.setGameApplyId(applyDO.getId());
                request.setUid(applyDO.getUid());
                multiGameManager.userDisagree(request);
            } else {
                MultiGameApplyCancelRequest request = new MultiGameApplyCancelRequest();
                request.setGameApplyId(applyDO.getId());
                request.setUid(applyDO.getUid());
                multiGameManager.cancel(request);
            }
        }

        // 维护麦位使用数量
        multiGameService.updateUsedSeatCount(liveRoomId);

    }


    private RtcGameInfo buildRtcInfo(Long liveRoomId, Long anchorUid, Long playerUid, String gameScene, String gameRoomSonaId) {
        RtcGameInfo rtcGameInfo = new RtcGameInfo();
        rtcGameInfo.setBizType(SUDGameConstant.SUD_MULTI_LINK_SCENE);
        rtcGameInfo.putParam("rtcBizType", SUDGameConstant.SUD_MULTI_LINK_SCENE);
        rtcGameInfo.putParam("user", multiGameCommonManager.buildSimpleUser(playerUid, liveRoomId));
        rtcGameInfo.putParam("anchorUid", anchorUid);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(gameRoomSonaId)) {
            rtcGameInfo.putParam("gameRoomSonaId", gameRoomSonaId);
        }
        // sud配置信息
        if (!StringUtils.isEmpty(gameScene)) {
            Map<String, SudGameConfigDTO> sudGameConfigDTOMap = sudApolloConfig.querySudGameConfigMap(liveRoomId);
            SudGameConfigDTO sudGameConfigDTO = sudGameConfigDTOMap.get(gameScene);
            rtcGameInfo.putParam("scene", gameScene);
            rtcGameInfo.putParam("sudGameConfig", sudGameConfigDTO);
        }
        return rtcGameInfo;
    }


    /**
     * 初始化RTC
     */
    private RTCStartDTO rtcStart(Long uid, Integer appId, LiveDTO liveDTO, String rtcBizId, String gameScene, String gameRoomSonaId) {
        RtcGameInfo rtcGameInfo = buildRtcInfo(liveDTO.getLiveRoomId(), liveDTO.getUid(), uid, gameScene, gameRoomSonaId);
        RTCStartRequest rtcStartRequest = new RTCStartRequest();

        rtcStartRequest.setBizType(SUDGameConstant.SUD_MULTI_LINK_SCENE);
        rtcStartRequest.setBizId(rtcBizId);
        rtcStartRequest.setAppId(liveDTO.getAppId());

        List<RTCStartRequest.RTCPlayer> playersList = Lists.newArrayList();
        RTCStartRequest.RTCPlayer anchor = new RTCStartRequest.RTCPlayer();
        anchor.setUid(liveDTO.getUid());
        anchor.setRoomId(liveDTO.getSonaRoomId());
        anchor.setStreamId(liveDTO.getStreamId());
        anchor.setAppId(liveDTO.getAppId());
        anchor.setExt(rtcGameInfo.getMap());
        if (!StringUtils.isEmpty(gameRoomSonaId)) {
            anchor.setSudRoomId(Long.valueOf(gameRoomSonaId));
        }

        RTCStartRequest.RTCPlayer user = new RTCStartRequest.RTCPlayer();
        user.setUid(uid);
        user.setRoomId(liveDTO.getSonaRoomId());
        user.setAppId(appId);
        user.setExt(rtcGameInfo.getMap());
        if (!StringUtils.isEmpty(gameRoomSonaId)) {
            user.setSudRoomId(Long.valueOf(gameRoomSonaId));
        }

        playersList.add(anchor);
        playersList.add(user);
        rtcStartRequest.setPlayersList(playersList);
        return sonaRPC.rtcStart(liveDTO.getAppId(), rtcStartRequest);
    }


    private void rtcEnd(String rtcBizId, Long sonaRoomId, List<MultiGameSeatDO> seatList) {
        try {
            RTCEndRequest rtcEndRequest = new RTCEndRequest();
            rtcEndRequest.setProductCode("LIVING");
            rtcEndRequest.setBizType(SUDGameConstant.SUD_MULTI_LINK_SCENE);
            rtcEndRequest.setBizId(rtcBizId);

            Map<String, Object> data = new HashMap<>();
            List<RTCEndRequest.EndBusinessInfo> userList = seatList.stream().filter(a -> a.getUid() != null && a.getUid() != 0).map(a -> {
                RTCEndRequest.EndBusinessInfo endBusinessInfo = new RTCEndRequest.EndBusinessInfo();
                endBusinessInfo.setRoomId(sonaRoomId);
                endBusinessInfo.setUid(a.getUid());
                endBusinessInfo.setExt(data);
                return endBusinessInfo;
            }).collect(Collectors.toList());
            rtcEndRequest.setBusinessInfoList(userList);
            sonaRPC.rtcEnd(rtcEndRequest);
        } catch (Exception e) {
            log.error("MultiGameFlowManager.rtcEnd error, sonaRoomId:{}, rtcBizId:{}", sonaRoomId, rtcBizId, e);
        }
    }


    private boolean isMultiSud(String scene) {
        if (StringUtils.isEmpty(scene)) {
            return true;
        }
        return scene.startsWith("SUD_MULTI") || scene.startsWith("SUG_");
    }


    public void roundStart(Long liveRoomId, Long anchorUid) {
        RedisService.RedisLocker redisLocker = redisCommonService.buildLock(RedisKeyEnum.MULTI_GAME_ROUND_START_KEY, anchorUid);
        if (!redisLocker.tryLock()) {
            throw new YppRunTimeException(ErrorCode.REPEAT_ERROR);
        }
        try {
            MultiGameConfigDO multiGameConfig = multiGameService.findMultiGameConfig(liveRoomId);
            if (multiGameConfig == null || !anchorUid.equals(multiGameConfig.getAnchorUid())) {
                throw new YppRunTimeException(ErrorCode.GAME_INFO_ERROR);
            }
            // 开始游戏
            boolean startGameRound = multiGameService.startGameRound(multiGameConfig);
            if (!startGameRound) {
                log.error("MultiGameFlowManager.roundStart error, liveRoomId:{}, anchorUid:{}", liveRoomId, anchorUid);
                throw new YppRunTimeException(ErrorCode.SYSTEM_BUSY_ERROR);
            }

            // 给用户中心发送中关注主播 "游戏中" 状态变更消息
            multiGameChatroomMsgSender.sendKafkaMsgForRelationService(anchorUid, liveRoomId, getSimpleStateDesc(NewMultiGameState.PLAYING));

            // 发送sud房间匹配消息
            multiGameChatroomMsgSender.sendMatchKafkaMsg(
                    OpTypeEnum.UPDATE,
                    multiGameConfig.getAnchorUid(),
                    multiGameConfig.getUsedSeatCount(),
                    multiGameConfig.getMatchSwitch() == 1,
                    NewMultiGameState.PLAYING.getCode(),
                    multiGameConfig.getGameScene(),
                    null,
                    liveRoomId);
        } finally {
            redisLocker.release();
        }
    }

    public void roundEnd(Long liveRoomId) {
        MultiGameConfigDO multiGameConfig = multiGameService.findMultiGameConfig(liveRoomId);
        Long anchorUid = multiGameConfig.getAnchorUid();
        RedisService.RedisLocker redisLocker = redisCommonService.buildLock(RedisKeyEnum.MULTI_GAME_ROUND_END_KEY, anchorUid);
        if (!redisLocker.tryLock()) {
            throw new YppRunTimeException(ErrorCode.REPEAT_ERROR);
        }
        try {
            if (multiGameConfig == null) {
                throw new YppRunTimeException(ErrorCode.GAME_INFO_ERROR);
            }
            // 开始游戏
            boolean endGameRound = multiGameService.endGameRound(multiGameConfig);
            if (!endGameRound) {
                log.error("MultiGameFlowManager.endGameRound error, liveRoomId:{}, anchorUid:{}", liveRoomId, anchorUid);
                throw new YppRunTimeException(ErrorCode.SYSTEM_BUSY_ERROR);
            }

            // 给用户中心发送中关注主播 "游戏中" 状态变更消息
            multiGameChatroomMsgSender.sendKafkaMsgForRelationService(anchorUid, liveRoomId, getSimpleStateDesc(NewMultiGameState.READY));

            // 发送sud房间匹配消息
            multiGameChatroomMsgSender.sendMatchKafkaMsg(
                    OpTypeEnum.UPDATE,
                    multiGameConfig.getAnchorUid(),
                    multiGameConfig.getUsedSeatCount(),
                    multiGameConfig.getMatchSwitch() == 1,
                    NewMultiGameState.READY.getCode(),
                    multiGameConfig.getGameScene(),
                    null,
                    liveRoomId);
        } finally {
            redisLocker.release();
        }
    }


    private String getMarket() {
        Client client = TraceUtil.getClient();
        return client != null ? client.getMarket() : null;
    }


    private String queryGameRoomSonaId(Long liveRoomId) {
        GameRoomDTO gameRoomDTO = gameRoomQueryRPC.findGameRoomByLiveRoomId(liveRoomId);
        return gameRoomDTO == null ? "" : gameRoomDTO.getGameSonaId();
    }

    public void endGameAndChange(Long liveRoomId, String gameScene) {
        MultiGameConfigDO multiGameConfig = multiGameService.findMultiGameConfig(liveRoomId);
        if (multiGameConfig == null) {
            log.info("MultiGameFlowManager.endGameAndChange error, multiGameConfig is null, liveRoomId:{}", liveRoomId);
            throw new YppRunTimeException(ErrorCode.GAME_INFO_ERROR);
        }
        // 变更游戏
        multiGameService.chooseGameScene(multiGameConfig, gameScene);
        log.info("MultiGameFlowManager.endGameAndChange chooseGameScene success, liveRoomId:{}, gameScene:{}", liveRoomId, gameScene);

        multiGameChatroomMsgSender.sendEndAndChangeGameMsg(liveRoomId, gameScene);
        log.info("MultiGameFlowManager.endGameAndChange endGameAndChange success, liveRoomId:{}, gameScene:{}", liveRoomId, gameScene);
    }

    public void sendMatchMsgWhenLiveStart(Long liveRoomId, Long anchorUid) {
        MultiGameConfigDO multiGameConfig = multiGameService.findMultiGameConfig(liveRoomId);
        if (multiGameConfig == null) {
            log.info("MultiGameFlowManager.sendMatchMsgWhenLiveStart error, multiGameConfig is null, liveRoomId:{}", liveRoomId);
            return;
        }

        String gameScene = multiGameRoundRecordService.selectMaxGameScene(anchorUid);
        if (StringUtils.isEmpty(gameScene)) {
            gameScene = SUD_MULTI_GOBANG;
        }
        multiGameService.chooseGameScene(multiGameConfig, gameScene);
        multiGameConfig = multiGameService.findMultiGameConfig(liveRoomId);
        multiGameConfig.setGameScene(gameScene);

        boolean started = !StringUtils.isEmpty(multiGameConfig.getGameScene())
                && NewMultiGameState.isStarting(multiGameConfig.getNewGameState());

        // 发送sud房间匹配消息
        multiGameChatroomMsgSender.sendMatchKafkaMsg(
                !started && multiGameConfig.getMatchSwitch() == 0 ? OpTypeEnum.REMOVE : OpTypeEnum.UPDATE,
                multiGameConfig.getAnchorUid(),
                multiGameConfig.getUsedSeatCount(),
                multiGameConfig.getMatchSwitch() == 1,
                multiGameConfig.getNewGameState(),
                multiGameConfig.getGameScene(),
                null,
                liveRoomId);
    }

    public void openSwitchOnce(Long liveRoomId, Long anchorUid, Integer appId, String appVersion) {
        GameOpenSwitchOnce record = new GameOpenSwitchOnce();
        record.setAnchorUid(anchorUid);
        GameMatchOpenOnceApollo.GameMatchOpenOnceConfig config = gameMatchOpenOnceApollo.getGameMatchOpenOnceConfig();
        if (!StringUtils.isEmpty(appVersion) && appId != null &&
                ((appId == 10 && VersionUtil.compare(appVersion, config.getBxVersionThreshold()) >= 0) ||
                        (appId == 30 && VersionUtil.compare(appVersion, config.getXxqVersionThreshold()) >= 0))) {
            if (appId == 10) {
                record.setBxVersion(appVersion);
            } else {
                record.setXxqVersion(appVersion);
            }
            record.setUpdateTime(new Date());
            int res = gameOpenSwitchOnceMapper.insertOrUpdateSelective(record);
            if (res == 1) {
                Boolean b = multiGameManager.matchSwitchChange(liveRoomId, true);
                log.info("MultiGameFlowManager.openSwitchOnce success, appId:{}, appVersion:{}, b: {}", appId, appVersion, b);
            }
        } else {
            log.info("MultiGameFlowManager.openSwitchOnce not new version, appId:{}, appVersion:{}", appId, appVersion);
        }
    }

    public List<OpenGameUser> filterPlayerList(Long liveRoomId, Long anchorUid, List<MultiGameSeatDO> seatDOList) {
        // seatDoList state 可能为 1 或 2
        List<Long> uidList = seatDOList
                .stream()
                .filter(seat -> seat.getState() == 2)
                .map(MultiGameSeatDO::getUid)
                .collect(Collectors.toList());
        Map<Long, UserInfo> userInfoBOMap = userManager.roomUserMap(uidList, liveRoomId);
        List<OpenGameUser> openGameUserList = new ArrayList<>();

        for (Long uid : uidList) {
            OpenGameUser openGameUser = new OpenGameUser();
            openGameUser.setUid(String.valueOf(uid));
            UserInfo userSimpleInfo = userInfoBOMap.get(uid);
            openGameUser.setNickName(userSimpleInfo.getUsername());
            openGameUser.setAvatar(userSimpleInfo.getAvatar());
            if (anchorUid.equals(uid)) {
                // 房主
                openGameUser.setSeatIndex(0);
                openGameUser.setIdentity(0);
                openGameUser.setReadyStatus(1);
            } else {
                // 其他用户
                openGameUser.setSeatIndex(-2);
                openGameUser.setIdentity(2);
                openGameUser.setReadyStatus(0);
            }
            openGameUserList.add(openGameUser);
        }

        return openGameUserList;
    }
}
